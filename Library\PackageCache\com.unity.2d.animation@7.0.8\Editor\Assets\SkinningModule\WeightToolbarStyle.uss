#AutoGenerateWeightImage {
    background-image: url("Icons/Light/Generate_Weights.png");
}


#WeightPainterSliderImage {
    background-image: url("Icons/Light/Weight_Slider.png");
}

#WeightPainterBrushImage {
    background-image: url("Icons/Light/Weight_Brush.png");
}

#BoneInfluenceWidgetImage {
    background-image: url("Icons/Light/Bone_Influence.png");
}

#SpriteInfluenceWidgetImage {
    background-image: url("Icons/Light/Sprite_Influence.png");
}

.Checked #AutoGenerateWeightImage {
    background-image: url("Icons/Selected/Generate_Weights.png");
}


.Checked #WeightPainterSliderImage {
    background-image: url("Icons/Selected/Weight_Slider.png");
}

.Checked #WeightPainterBrushImage {
    background-image: url("Icons/Selected/Weight_Brush.png");
}

.Checked #BoneInfluenceWidgetImage {
    background-image: url("Icons/Selected/Bone_Influence.png");
}

.Checked #SpriteInfluenceWidgetImage {
    background-image: url("Icons/Selected/Sprite_Influence.png");
}

.Dark #AutoGenerateWeightImage {
    background-image: url("Icons/Dark/d_Generate_Weights.png");
}


.Dark #WeightPainterSliderImage {
    background-image: url("Icons/Dark/d_Weight_Slider.png");
}

.Dark #WeightPainterBrushImage {
    background-image: url("Icons/Dark/d_Weight_Brush.png");
}

.Dark #BoneInfluenceWidgetImage {
    background-image: url("Icons/Dark/d_Bone_Influence.png");
}

.Dark #SpriteInfluenceWidgetImage {
    background-image: url("Icons/Dark/d_Sprite_Influence.png");
}
