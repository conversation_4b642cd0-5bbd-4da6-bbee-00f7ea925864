using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GameRoot : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {
        SRDebug.Init();
        SRDebug.Instance.ShowDebugPanel();
        SRDebug.Instance.HideDebugPanel();
        SRDebug.Instance.


        // In Unity
        SRDebug.Instance.AddOptionContainer(MyOptions.Instance);
    }

    // Update is called once per frame
    void Update()
    {

    }
}


// In MyGameCode.dll
class MyOptions
{
    public static readonly MyOptions Instance = new MyOptions();

    public void 无敌护盾() { /* ... */ }
    public float SomeProperty { get; set; }
}

