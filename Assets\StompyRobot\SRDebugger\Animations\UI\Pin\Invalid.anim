%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Invalid
  serializedVersion: 4
  m_AnimationType: 2
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 760
        outSlope: 760
        tangentMode: 10
      - time: .0500000007
        value: 38
        inSlope: -77.285675
        outSlope: -77.285675
        tangentMode: 10
      - time: .166666672
        value: -68.6999969
        inSlope: -146.952332
        outSlope: -146.952332
        tangentMode: 10
      - time: .316666663
        value: 24.3999996
        inSlope: 48.333313
        outSlope: 48.333313
        tangentMode: 10
      - time: .416666657
        value: -28
        inSlope: -122.000015
        outSlope: -122.000015
        tangentMode: 10
      - time: .516666651
        value: 0
        inSlope: 280.000031
        outSlope: 280.000031
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: 
    classID: 224
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 1460864421
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 538195251
      script: {fileID: 0}
      classID: 224
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_StartTime: 0
    m_StopTime: .516666651
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 760
        outSlope: 760
        tangentMode: 10
      - time: .0500000007
        value: 38
        inSlope: -77.285675
        outSlope: -77.285675
        tangentMode: 10
      - time: .166666672
        value: -68.6999969
        inSlope: -146.952332
        outSlope: -146.952332
        tangentMode: 10
      - time: .316666663
        value: 24.3999996
        inSlope: 48.333313
        outSlope: 48.333313
        tangentMode: 10
      - time: .416666657
        value: -28
        inSlope: -122.000015
        outSlope: -122.000015
        tangentMode: 10
      - time: .516666651
        value: 0
        inSlope: 280.000031
        outSlope: 280.000031
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_AnchoredPosition.y
    path: 
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_Events: []
