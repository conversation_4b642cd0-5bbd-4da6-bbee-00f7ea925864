-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.ref.dll"
-define:UNITY_2021_3_14
-define:UNITY_2021_3
-define:UNITY_2021
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:USE_SEARCH_ENGINE_API
-define:USE_SEARCH_TABLE
-define:USE_SEARCH_MODULE
-define:USE_PROPERTY_DATABASE
-define:USE_SEARCH_EXTENSION_API
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_COLLAB
-define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:ENABLE_WEBSOCKET_HOST
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:UNITY_PRO_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.burst@1.7.3/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.burst@1.7.3/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.burst@1.7.3/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.burst@1.7.3/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.burst@1.7.3/Unity.Burst.Unsafe.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.collab-proxy@1.17.6/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.collab-proxy@1.17.6/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.collab-proxy@1.17.6/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.collab-proxy@1.17.6/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.visualscripting@1.7.8/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.visualscripting@1.7.8/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.visualscripting@1.7.8/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"C:/build/output/unity/unity/build/libcache/Library/PackageCache/com.unity.visualscripting@1.7.8/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/build/output/unity/unity/build/WindowsEditor/x64/Release/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/AngleRangeController.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/AngleRangeGUI.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/AngleRangeView.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/ContextMenu.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/EditorSpriteGUIUtility.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/ObjectMenuCreation/AssetCreation.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/ObjectMenuCreation/GameObjectCreation.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SceneDragAndDrop.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteSelector.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeAnalytics.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeAssetPostProcessor.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeControllerEditor.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeEditor.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeEditorAnalytics.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeEditorGUI.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeEditorTool.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeEditorUtility.cs"
"Library/PackageCache/com.unity.2d.spriteshape@7.0.6/Editor/SpriteShapeHandleUtility.cs"
-langversion:9.0

/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319

/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0

/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.AdditionalFile.txt"